import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Search, X } from "lucide-react";

interface EmojiPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onEmojiSelect: (emoji: string) => void;
  currentEmoji?: string;
}

const EMOJI_CATEGORIES = {
  recent: ["📁", "📂", "🎨", "💼", "🚀", "⭐", "💡", "🔥"],
  folders: ["📁", "📂", "🗂️", "📋", "📊", "📈", "📉", "📄", "📃", "📑", "🗃️", "🗄️"],
  work: ["💼", "🎯", "🔧", "⚙️", "🔨", "🛠️", "📦", "🎨", "✏️", "📝", "💻", "⌨️", "🖥️", "📱"],
  symbols: ["⭐", "🔥", "💡", "🚀", "🎉", "🏆", "💎", "🔮", "⚡", "🌟", "✨", "💫", "🎊", "🎈"],
  nature: ["🌱", "🌿", "🍀", "🌸", "🌺", "🌻", "🌷", "🌹", "🌼", "🌵", "🌲", "🌳", "🌴", "🌾"],
  hearts: ["❤️", "💙", "💚", "💛", "🧡", "💜", "🖤", "🤍", "🤎", "💖", "💕", "💗", "💘", "💝"],
  faces: ["😀", "😊", "🤔", "😎", "🤓", "😴", "🤗", "😇", "🙂", "😉", "😍", "🥰", "😋", "😌"],
};

const CATEGORY_ICONS = {
  recent: "🕒",
  folders: "📁",
  work: "💼",
  symbols: "⭐",
  nature: "🌿",
  hearts: "❤️",
  faces: "😊",
};

export default function EmojiPicker({ isOpen, onClose, onEmojiSelect, currentEmoji }: EmojiPickerProps) {
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof EMOJI_CATEGORIES>("recent");
  const [searchQuery, setSearchQuery] = useState("");
  const [hoveredEmoji, setHoveredEmoji] = useState<string | null>(null);

  // Reset search when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSearchQuery("");
      setSelectedCategory("recent");
    }
  }, [isOpen]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    onClose();
  };

  // Filter emojis based on search
  const filteredEmojis = searchQuery
    ? Object.values(EMOJI_CATEGORIES).flat().filter(emoji =>
        emoji.includes(searchQuery) ||
        // You could add emoji names/descriptions here for better search
        false
      )
    : EMOJI_CATEGORIES[selectedCategory];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-text-primary text-lg font-semibold">
              Choose Folder Icon
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-background-hover"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-4 flex-1 min-h-0">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-text-muted" />
            <input
              type="text"
              placeholder="Search icons..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background-input border border-border rounded-md text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-accent-blue/50 focus:border-accent-blue"
            />
          </div>

          {!searchQuery && (
            <>
              {/* Category Tabs */}
              <div className="flex flex-wrap gap-1">
                {Object.entries(EMOJI_CATEGORIES).map(([category, emojis]) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "ghost"}
                    size="sm"
                    className="text-xs capitalize flex items-center gap-1.5 px-3 py-1.5"
                    onClick={() => setSelectedCategory(category as keyof typeof EMOJI_CATEGORIES)}
                  >
                    <span className="text-sm">{CATEGORY_ICONS[category as keyof typeof CATEGORY_ICONS]}</span>
                    {category}
                    <span className="text-xs opacity-60">({emojis.length})</span>
                  </Button>
                ))}
              </div>
            </>
          )}

          {/* Emoji Grid */}
          <div className="flex-1 min-h-0">
            <div className="grid grid-cols-6 gap-2 max-h-64 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-background-hover scrollbar-track-transparent">
              {filteredEmojis.map((emoji, index) => (
                <Button
                  key={`${emoji}-${index}`}
                  variant="ghost"
                  className={`h-12 w-12 text-2xl p-0 hover:bg-background-hover hover:scale-110 transition-all duration-150 rounded-lg ${
                    currentEmoji === emoji
                      ? "bg-accent-blue/20 border-2 border-accent-blue ring-2 ring-accent-blue/30"
                      : hoveredEmoji === emoji
                      ? "bg-background-hover/80"
                      : ""
                  }`}
                  onClick={() => handleEmojiClick(emoji)}
                  onMouseEnter={() => setHoveredEmoji(emoji)}
                  onMouseLeave={() => setHoveredEmoji(null)}
                  title={`Select ${emoji}`}
                >
                  {emoji}
                </Button>
              ))}
            </div>

            {searchQuery && filteredEmojis.length === 0 && (
              <div className="text-center py-8 text-text-muted">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No icons found for "{searchQuery}"</p>
              </div>
            )}
          </div>

          {/* Current Selection & Preview */}
          <div className="flex-shrink-0 border-t border-border pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className="text-sm text-text-secondary">Current:</span>
                <div className="flex items-center gap-2">
                  <span className="text-3xl">{currentEmoji || "📁"}</span>
                  <span className="text-sm text-text-muted">→</span>
                  <span className="text-3xl">{hoveredEmoji || currentEmoji || "📁"}</span>
                </div>
              </div>

              {hoveredEmoji && hoveredEmoji !== currentEmoji && (
                <Button
                  onClick={() => handleEmojiClick(hoveredEmoji)}
                  size="sm"
                  className="bg-accent-blue hover:bg-accent-blue/80"
                >
                  Select {hoveredEmoji}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
